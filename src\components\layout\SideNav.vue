<!-- src/components/layout/SideNav.vue -->
<template>
  <nav class="side-nav">
    <!-- 알림 벨 컴포넌트: 오른쪽 상단에 배치 -->
    <div class="notification-container">
      <div class="notification-wrapper" v-if="authStore.isAuthenticated">
        <NotificationBell @toggle="toggleNotificationList" />
        <!-- 알림 목록 컴포넌트 -->
        <NotificationList
          v-if="authStore.isAuthenticated"
          :is-open="showNotificationList"
          @close="closeNotificationList"
        />
      </div>
    </div>
    
    <!-- 메뉴 로딩 상태 -->
    <div v-if="isLoadingMenus" class="menu-loading">
      <p>메뉴를 불러오는 중...</p>
    </div>

    <!-- 메뉴 로드 오류 시 알림 -->
    <div v-else-if="menuLoadError" class="menu-error">
      <p>메뉴를 불러오는 중에 오류가 발생했습니다.</p>
      <button @click="retryLoadMenus" class="retry-menu-btn">다시 시도</button>
    </div>

    <!-- 메뉴가 없을 때 알림 -->
    <div v-else-if="!isLoadingMenus && !menuLoadError && visibleMenus.length === 0" class="menu-empty">
      <p>할당된 메뉴가 없습니다.</p>
    </div>

    <!-- 동적 메뉴 렌더링 -->
    <ul v-else-if="visibleMenus.length > 0">
      <li v-for="route in visibleMenus" :key="route.name" class="parent-item">
        <!-- 이벤트 관리 메뉴 -->
        <template v-if="route.name === 'events-management'">
          <div @click="toggleEvents" class="nav-link clickable">
            {{ route.meta.title }}
            <span class="arrow" :class="{ open: eventsOpen }">▾</span>
          </div>
          <ul v-if="eventsOpen" class="sub-menu">
            <li v-for="child in eventsChildRoutes" :key="child.name" class="sub-item">
              <router-link :to="{ name: child.name }" class="nav-link">{{ child.title }}</router-link>
            </li>
          </ul>
        </template>
        <!-- 통계 메뉴 -->
        <template v-else-if="route.name === 'statistics'">
          <div @click="toggleStatistics" class="nav-link clickable">
            {{ route.meta.title }}
            <span class="arrow" :class="{ open: statisticsOpen }">▾</span>
          </div>
          <ul v-if="statisticsOpen" class="sub-menu">
            <li v-for="child in statisticsChildRoutes" :key="child.name" class="sub-item">
              <router-link :to="{ name: child.name }" class="nav-link">{{ child.title }}</router-link>
            </li>
          </ul>
        </template>
        <!-- QR 관리 메뉴 -->
        <template v-else-if="route.name === 'qr-management'">
          <div @click="toggleQrManagement" class="nav-link clickable">
            {{ route.meta.title }}
            <span class="arrow" :class="{ open: qrManagementOpen }">▾</span>
          </div>
          <ul v-if="qrManagementOpen" class="sub-menu">
            <li v-for="child in qrManagementChildRoutes" :key="child.name" class="sub-item">
              <a v-if="child.openInNewTab"
                 :href="router.resolve({ name: child.name }).href"
                 target="_blank"
                 class="nav-link">{{ child.title }}</a>
              <router-link v-else :to="{ name: child.name }" class="nav-link">{{ child.title }}</router-link>
            </li>
          </ul>
        </template>
        <!-- QnA 메뉴 (단일 메뉴) -->
        <template v-else-if="route.name === 'qna-management'">
          <router-link :to="{ name: 'qna-management' }" class="nav-link">{{ route.meta.title }}</router-link>
        </template>
        <!-- 기본 메뉴 -->
        <router-link v-else :to="{ name: route.name }" class="nav-link">{{ route.meta.title }}</router-link>
      </li>
    </ul>

    <!-- SUPER_ADMIN 전용 메뉴 관리 메뉴 -->
    <ul v-if="isSuperAdmin">
      <li class="parent-item">
        <router-link :to="{ name: 'menu-management' }" class="nav-link">메뉴 관리</router-link>
      </li>
    </ul>

    <!-- 프로젝트 정보 표시 -->
    <div v-if="authStore.user" class="project-info" :class="{ 'no-project': !hasProjects }">
      <div v-if="hasProjects" class="project-container">
        <span class="project-label">프로젝트 선택:</span>

        <!-- 프로젝트 선택 드롭다운 -->
        <select
          v-model.number="authStore.selectedProjectIndex"
          class="project-select"
          @change="changeProject"
        >
          <!-- SUPER_ADMIN인 경우 '전체 프로젝트' 옵션 추가 -->
          <option v-if="isSuperAdmin" :value="-1">전체 프로젝트</option>
          <option
            v-for="(project, index) in authStore.userProjects"
            :key="project.projectId"
            :value="index"
          >
            {{ project.projectName }}
          </option>
        </select>
      </div>
      <span v-else class="no-project-name">할당된 프로젝트 없음</span>
    </div>

    <!-- Logout button -->
    <button v-if="authStore.isAuthenticated" @click="logout" class="logout-button">로그아웃</button>
  </nav>
</template>

<script setup>
import { computed, watchEffect, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useNotificationStore } from '@/stores/notificationStore';
import NotificationBell from '@/components/notification/NotificationBell.vue';
import NotificationList from '@/components/notification/NotificationList.vue';
import {
  getAccessibleMenuTree,
  convertBackendMenuToTreeData
} from '@/api/menu';

const router = useRouter();
const authStore = useAuthStore();
const notificationStore = useNotificationStore();

// 알림 관련 상태 및 함수
const showNotificationList = ref(false);

// 백엔드에서 가져온 접근 가능한 메뉴들
const accessibleMenus = ref([]);
const isLoadingMenus = ref(false);
const menuLoadError = ref(false);
const menuLoadAttempted = ref(false);

// 접근 가능한 메뉴 데이터 로드
const loadAccessibleMenus = async () => {
  // 이미 로딩 중이거나 이미 시도했으면 중복 요청 방지
  if (isLoadingMenus.value || menuLoadAttempted.value) return;

  try {
    isLoadingMenus.value = true;
    menuLoadAttempted.value = true;
    menuLoadError.value = false;

    const response = await getAccessibleMenuTree();
    const backendMenuData = response.data || response;

    // 백엔드 데이터를 프론트엔드 형태로 변환
    if (Array.isArray(backendMenuData)) {
      accessibleMenus.value = convertBackendMenuToTreeData(backendMenuData);
    } else {
      accessibleMenus.value = [];
    }
  } catch (error) {
    menuLoadError.value = true;

    // 백엔드 오류 메시지 추출
    let errorMessage = '메뉴를 불러올 수 없습니다.';
    if (error.response?.data?.success === false && error.response.data.error) {
      errorMessage = error.response.data.error.message;
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    }

    // 403 오류인 경우 조용히 처리 (권한 없음)
    if (error.response?.status === 403) {
      console.warn(`메뉴 접근 권한이 없습니다: ${errorMessage}`);
      accessibleMenus.value = [];
    } else {
      console.error(`접근 가능한 메뉴 로드 실패: ${errorMessage}`, error);
      accessibleMenus.value = [];
    }
  } finally {
    isLoadingMenus.value = false;
  }
};

// 알림 목록 토글
const toggleNotificationList = () => {
  showNotificationList.value = !showNotificationList.value;
};

// 알림 목록 닫기
const closeNotificationList = () => {
  showNotificationList.value = false;
};

// 컴포넌트 마운트 시 읽지 않은 알림 개수 가져오기 및 메뉴 로드
onMounted(async () => {
  if (authStore.isAuthenticated) {
    try {
      // 알림 개수 가져오기
      await notificationStore.fetchUnreadCountAction();

      // 접근 가능한 메뉴 로드
      await loadAccessibleMenus();
    } catch (error) {
      console.error('초기 데이터 로드 중 오류가 발생했습니다:', error);
    }
  }
});

// 인증 상태 변경 시 메뉴 다시 로드 (한 번만 시도)
watchEffect(() => {
  if (authStore.isAuthenticated && !isLoadingMenus.value && !menuLoadAttempted.value) {
    loadAccessibleMenus();
  }
});

const userRole = computed(() => authStore.user?.roleId);

// SUPER_ADMIN 여부 확인
const isSuperAdmin = computed(() => {
  return authStore.user?.roleId === 'SUPER_ADMIN';
});

// 프로젝트 관련 계산 속성
const hasProjects = computed(() => {
  return authStore.user?.projects && authStore.user.projects.length > 0;
});

// 현재 프로젝트 ID
const currentProjectId = computed(() => {
  return authStore.currentProject?.projectId || '-';
});

// 프로젝트 변경 함수
const changeProject = () => {
  // 선택된 인덱스를 숫자로 변환 (select 옵션은 문자열로 처리될 수 있음)
  const selectedIndex = Number(authStore.selectedProjectIndex);

  // auth 스토어의 selectProject 함수 호출 (전체 프로젝트 모드도 처리)
  const success = authStore.selectProject(selectedIndex);

  if (success) {
    // 커스텀 이벤트 발생 (모든 페이지에서 이 이벤트를 감지하여 데이터 다시 로드)
    window.dispatchEvent(new CustomEvent('project-changed', {
      detail: {
        projectId: authStore.currentProject?.projectId,
        projectName: authStore.currentProject?.projectName
      }
    }));

    // 현재 페이지가 사용자 관리 페이지인 경우 특별 처리 (새로고침 필요)
    const currentRoute = router.currentRoute.value;
    if (currentRoute.name === 'admin-user-management') {
      // 사용자 관리 페이지는 프로젝트 변경 시 새로고침 필요
      window.location.reload();
      return;
    }
  } else {
    // 실패 시 첫 번째 프로젝트로 되돌림 (SUPER_ADMIN이 아닌 경우)
    if (selectedIndex === -1 && authStore.user?.roleId !== 'SUPER_ADMIN') {
      authStore.selectedProjectIndex = 0;
    }
  }
};



// 메뉴 설정이 적용된 표시 가능한 메뉴들 (백엔드 API 기반만 사용)
const visibleMenus = computed(() => {
  // 백엔드에서 메뉴를 성공적으로 가져온 경우만 처리
  if (!menuLoadError.value && accessibleMenus.value.length > 0) {
    return accessibleMenus.value
      .filter(node => node.visible && node.status === 'ACTIVE')
      .map(node => ({
        name: node.routeName || node.menuCode,
        path: node.routePath,
        meta: {
          title: node.label,
          permissions: [], // 백엔드에서 이미 권한 필터링됨
          openInNewTab: node.openInNewTab
        }
      }));
  }

  // 오류가 있거나 메뉴가 없는 경우 빈 배열 반환
  return [];
});


const eventsChildRoutes = computed(() => {
  const eventsMenu = accessibleMenus.value.find(menu =>
    menu.routeName === 'events-management' || menu.menuCode === 'EVENTS_MANAGEMENT'
  );

  if (eventsMenu && eventsMenu.nodes) {
    return eventsMenu.nodes
      .filter(child => child.visible && child.status === 'ACTIVE')
      .map(child => ({
        name: child.routeName || child.menuCode,
        title: child.label
      }));
  }

  return [];
});
const eventsOpen = ref(false);
const toggleEvents = () => { eventsOpen.value = !eventsOpen.value; };

// 통계 메뉴 관련 상태 및 함수
const statisticsChildRoutes = computed(() => {
  const statisticsMenu = accessibleMenus.value.find(menu =>
    menu.routeName === 'statistics' || menu.menuCode === 'STATISTICS'
  );

  if (statisticsMenu && statisticsMenu.nodes) {
    return statisticsMenu.nodes
      .filter(child => child.visible && child.status === 'ACTIVE')
      .map(child => ({
        name: child.routeName || child.menuCode,
        title: child.label
      }));
  }

  return [];
});

const statisticsOpen = ref(false);

const toggleStatistics = () => {
  statisticsOpen.value = !statisticsOpen.value;
};

// QnA 메뉴 관련 상태 및 함수
const qnaChildRoutes = computed(() => {
  const list = [];

  // QnA 라우트 자식 메뉴 추출
  router.options.routes
    .filter(route => route.name === 'qna-management')
    .forEach(route => {
      if (route.children) {
        route.children.forEach(child => {
          if (child.meta && child.meta.title) {
            list.push({
              name: child.name,
              title: child.meta.title
            });
          }
        });
      }
    });

  return list;
});

const qnaOpen = ref(false);

const toggleQna = () => {
  qnaOpen.value = !qnaOpen.value;
};

// QR 관리 메뉴 관련 상태 및 함수
const qrManagementChildRoutes = computed(() => {
  const qrMenu = accessibleMenus.value.find(menu =>
    menu.routeName === 'qr-management' || menu.menuCode === 'QR_MANAGEMENT'
  );

  if (qrMenu && qrMenu.nodes) {
    return qrMenu.nodes
      .filter(child => child.visible && child.status === 'ACTIVE')
      .map(child => ({
        name: child.routeName || child.menuCode,
        title: child.label,
        openInNewTab: child.openInNewTab || false
      }));
  }

  return [];
});

const qrManagementOpen = ref(false);

const toggleQrManagement = () => {
  qrManagementOpen.value = !qrManagementOpen.value;
};

// 메뉴 다시 로드 (수동 재시도)
const retryLoadMenus = async () => {
  menuLoadAttempted.value = false;
  menuLoadError.value = false;
  accessibleMenus.value = [];
  await loadAccessibleMenus();
};

// 로그아웃 함수 (Auth 스토어의 logout 액션 호출)
const logout = async () => {
  try {
    await authStore.logout();
    // 로그아웃 성공 후 로그인 페이지로 리다이렉트
    router.push({ name: 'login' });
  } catch (error) {
    console.error('Logout failed:', error);
    // 필요한 경우 사용자에게 에러 메시지 표시
  }
};
</script>

<style scoped>
.side-nav {
  width: 250px;
  background-color: #f8f9fa;
  padding: 20px 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 알림 관련 스타일 */
.notification-container {
  position: fixed;
  top: 15px;
  right: 20px;
  z-index: 1000;
}

.notification-wrapper {
  margin: 0;
  padding: 0;
}

.side-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
}

.side-nav li {
  margin-bottom: 5px;
}

.nav-link {
  display: block;
  padding: 12px 20px;
  text-decoration: none;
  color: #333;
  transition: all 0.3s;
  border-radius: 0;
  font-size: 0.95rem;
}

.nav-link:hover,
.nav-link.router-link-active {
  background-color: #e9ecef;
  color: #007bff;
  border-left: 3px solid #007bff;
}

.nav-link.clickable {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.arrow {
  transition: transform 0.3s ease;
  font-size: 0.8rem;
  margin-left: 5px;
}

.arrow.open {
  transform: rotate(180deg);
}

.sub-menu {
  list-style: none;
  padding-left: 15px;
  margin: 0;
  border-left: 2px solid #dee2e6;
  background-color: #f1f3f5;
}

.sub-item .nav-link {
  font-size: 0.9rem;
  color: #495057;
  padding: 10px 15px;
}

/* 프로젝트 정보 스타일 */
.project-info {
  margin: 15px;
  padding: 12px;
  background-color: #f0f0f0;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 프로젝트가 없을 때 스타일 */
.project-info.no-project {
  background-color: #ffe6e6;
  border: 1px dashed #e74c3c;
}

.project-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.project-label {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 2px;
  font-weight: 500;
}

/* 프로젝트 선택 드롭다운 스타일 */
.project-select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 0.9rem;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.project-select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* 현재 프로젝트 정보 스타일 */
.current-project-info {
  display: flex;
  justify-content: center;
  margin-top: 5px;
}

.project-id {
  font-size: 0.85rem;
  color: #666;
  background-color: #e8e8e8;
  padding: 3px 8px;
  border-radius: 4px;
}

.project-name {
  font-weight: bold;
  color: #333;
  font-size: 0.95rem;
}

.no-project-name {
  font-weight: bold;
  color: #e74c3c;
  font-size: 0.9rem;
  font-style: italic;
}

.logout-button {
  margin: 15px;
  padding: 12px;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
  width: calc(100% - 30px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logout-button:hover {
  background-color: #c0392b;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.logout-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.side-nav li.sub-item .nav-link {
  padding-left: 30px;
}

/* 메뉴 로딩, 오류, 빈 상태 스타일 */
.menu-loading,
.menu-error,
.menu-empty {
  padding: 20px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

.menu-error {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  margin: 10px 15px;
}

.menu-empty {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin: 10px 15px;
  color: #6c757d;
}

.retry-menu-btn {
  margin-top: 10px;
  padding: 6px 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.retry-menu-btn:hover {
  background-color: #0056b3;
}

/* 모바일 최적화 */
@media (max-width: 767px) {
  .side-nav {
    width: 250px;
    background-color: #fff;
  }

  .nav-link {
    padding: 15px 20px;
    font-size: 1rem;
  }

  .sub-item .nav-link {
    padding: 12px 15px;
    font-size: 0.95rem;
  }

  .logout-button {
    padding: 15px;
    font-size: 1rem;
  }
}

</style>
